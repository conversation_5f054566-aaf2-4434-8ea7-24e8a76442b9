'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  HomeIcon,
  UsersIcon,
  BuildingStorefrontIcon,
  TruckIcon,
  ShoppingBagIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CogIcon,
  BellIcon,
  DocumentTextIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  XMarkIcon,
  ChevronUpIcon,
  ChevronDownIcon,

  PresentationChartLineIcon,
  BanknotesIcon,
  ShieldCheckIcon,
  MegaphoneIcon,
  PhotoIcon,
  PencilSquareIcon,
  TagIcon,
  GiftIcon,
  StarIcon,
  ChatBubbleLeftRightIcon,
  ClockIcon,
  MapPinIcon,
  CreditCardIcon,
  ChartPieIcon,
  DocumentChartBarIcon,
  AdjustmentsHorizontalIcon,
  KeyIcon,
  UserCircleIcon,
} from '@heroicons/react/24/outline';

// Type definitions for navigation structure
interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
}

interface NavigationCategory {
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  items: NavigationItem[];
}

// Professional menu categorization structure with category icons
const navigationCategories: NavigationCategory[] = [
  {
    name: 'Overview',
    icon: HomeIcon,
    items: [
      { name: 'Dashboard', href: '/admin/dashboard', icon: HomeIcon },
    ]
  },
  {
    name: 'User Management',
    icon: UsersIcon,
    items: [
      { name: 'All Users', href: '/admin/users', icon: UsersIcon },
      { name: 'Customers', href: '/admin/customers', icon: UserGroupIcon },
      { name: 'Vendors', href: '/admin/vendors', icon: BuildingStorefrontIcon },
      { name: 'Drivers', href: '/admin/drivers', icon: TruckIcon },
    ]
  },
  {
    name: 'Operations',
    icon: ShoppingBagIcon,
    items: [
      { name: 'Orders', href: '/admin/orders', icon: ShoppingBagIcon },
      { name: 'Disputes', href: '/admin/disputes', icon: ExclamationTriangleIcon },
      { name: 'Notifications', href: '/admin/notifications', icon: BellIcon },
      { name: 'Reviews', href: '/admin/reviews', icon: StarIcon },
      { name: 'Support Chat', href: '/admin/support', icon: ChatBubbleLeftRightIcon },
    ]
  },
  {
    name: 'Content Management',
    icon: PencilSquareIcon,
    items: [
      { name: 'CMS Dashboard', href: '/admin/cms-dashboard', icon: PencilSquareIcon },
      { name: 'Media Library', href: '/admin/cms/media', icon: PhotoIcon },
      { name: 'Promotions', href: '/admin/cms/promotions', icon: MegaphoneIcon },
      { name: 'Banners', href: '/admin/cms/banners', icon: TagIcon },
    ]
  },
  {
    name: 'Marketing',
    icon: MegaphoneIcon,
    items: [
      { name: 'Campaigns', href: '/admin/marketing/campaigns', icon: MegaphoneIcon },
      { name: 'Coupons', href: '/admin/marketing/coupons', icon: GiftIcon },
      { name: 'Loyalty Program', href: '/admin/marketing/loyalty', icon: StarIcon },
      { name: 'Push Notifications', href: '/admin/marketing/push', icon: BellIcon },
    ]
  },
  {
    name: 'Analytics & Reports',
    icon: ChartBarIcon,
    items: [
      { name: 'Overview Analytics', href: '/admin/analytics', icon: ChartBarIcon },
      { name: 'Sales Reports', href: '/admin/reports/sales', icon: DocumentChartBarIcon },
      { name: 'User Reports', href: '/admin/reports/users', icon: ChartPieIcon },
      { name: 'Performance', href: '/admin/reports/performance', icon: PresentationChartLineIcon },
      { name: 'Custom Reports', href: '/admin/reports/custom', icon: DocumentTextIcon },
    ]
  },
  {
    name: 'Financial',
    icon: CurrencyDollarIcon,
    items: [
      { name: 'Payouts', href: '/admin/payouts', icon: CurrencyDollarIcon },
      { name: 'Transactions', href: '/admin/financial/transactions', icon: CreditCardIcon },
      { name: 'Revenue', href: '/admin/financial/revenue', icon: BanknotesIcon },
      { name: 'Commissions', href: '/admin/financial/commissions', icon: ChartPieIcon },
    ]
  },
  {
    name: 'Logistics',
    icon: TruckIcon,
    items: [
      { name: 'Delivery Zones', href: '/admin/logistics/zones', icon: MapPinIcon },
      { name: 'Delivery Times', href: '/admin/logistics/times', icon: ClockIcon },
      { name: 'Fleet Management', href: '/admin/logistics/fleet', icon: TruckIcon },
    ]
  },
  {
    name: 'System',
    icon: CogIcon,
    items: [
      { name: 'General Settings', href: '/admin/settings', icon: CogIcon },
      { name: 'API Configuration', href: '/admin/settings/api', icon: AdjustmentsHorizontalIcon },
      { name: 'Security', href: '/admin/settings/security', icon: ShieldCheckIcon },
      { name: 'Admin Accounts', href: '/admin/settings/admins', icon: UserCircleIcon },
      { name: 'Access Keys', href: '/admin/settings/keys', icon: KeyIcon },
    ]
  }
];

interface AdminSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  isCollapsed?: boolean;
  onExpandAndNavigate?: (href: string, categoryName: string) => void;
}

// Custom hook for JavaScript-based scrolling with independent state preservation
function useJavaScriptScrolling(isCollapsed: boolean) {
  // Independent scroll positions for collapsed and expanded states
  const [expandedScrollPosition, setExpandedScrollPosition] = useState(0);
  const [collapsedScrollPosition, setCollapsedScrollPosition] = useState(0);
  const [canScrollUp, setCanScrollUp] = useState(false);
  const [canScrollDown, setCanScrollDown] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const scrollAnimationRef = useRef<number>();
  const lastWheelTime = useRef<number>(0);
  const scrollVelocity = useRef<number>(0);
  const momentumAnimationRef = useRef<number>();

  // Get current scroll position based on collapsed state
  const currentScrollPosition = isCollapsed ? collapsedScrollPosition : expandedScrollPosition;

  // Set scroll position based on collapsed state
  const setCurrentScrollPosition = useCallback((position: number) => {
    if (isCollapsed) {
      setCollapsedScrollPosition(position);
    } else {
      setExpandedScrollPosition(position);
    }
  }, [isCollapsed]);

  // Calculate scroll boundaries
  const updateScrollState = useCallback(() => {
    const container = scrollContainerRef.current;
    const content = contentRef.current;

    if (!container || !content) return;

    const containerHeight = container.clientHeight;
    const contentHeight = content.scrollHeight;
    const maxScroll = Math.max(0, contentHeight - containerHeight);

    setCanScrollUp(currentScrollPosition > 0);
    setCanScrollDown(currentScrollPosition < maxScroll);
  }, [currentScrollPosition]);

  // Professional smooth scroll with optimized easing
  const smoothScrollTo = useCallback((targetPosition: number, duration: number = 180) => {
    const container = scrollContainerRef.current;
    const content = contentRef.current;

    if (!container || !content) return;

    const maxScroll = Math.max(0, content.scrollHeight - container.clientHeight);
    const clampedTarget = Math.max(0, Math.min(targetPosition, maxScroll));

    if (scrollAnimationRef.current) {
      cancelAnimationFrame(scrollAnimationRef.current);
    }
    if (momentumAnimationRef.current) {
      cancelAnimationFrame(momentumAnimationRef.current);
    }

    const startPosition = currentScrollPosition;
    const distance = clampedTarget - startPosition;

    if (Math.abs(distance) < 0.5) { // Lower threshold for smoother micro-movements
      setCurrentScrollPosition(clampedTarget);
      setIsScrolling(false);
      return;
    }

    const startTime = performance.now();

    const animateScroll = (currentTime: number) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // Professional easing curve (ease-out cubic - like iOS/macOS)
      const easeOut = 1 - Math.pow(1 - progress, 3);
      const currentPosition = startPosition + (distance * easeOut);

      setCurrentScrollPosition(currentPosition);

      if (progress < 1) {
        scrollAnimationRef.current = requestAnimationFrame(animateScroll);
      } else {
        setIsScrolling(false);
      }
    };

    setIsScrolling(true);
    scrollAnimationRef.current = requestAnimationFrame(animateScroll);
  }, [currentScrollPosition, setCurrentScrollPosition]);

  // Natural momentum scrolling with professional physics
  const applyMomentum = useCallback(() => {
    // Much lower threshold for natural momentum continuation (like iOS/Android)
    if (Math.abs(scrollVelocity.current) < 0.1) {
      scrollVelocity.current = 0;
      setIsScrolling(false);
      return;
    }

    const newPosition = currentScrollPosition + scrollVelocity.current;
    const container = scrollContainerRef.current;
    const content = contentRef.current;

    if (container && content) {
      const maxScroll = Math.max(0, content.scrollHeight - container.clientHeight);
      const clampedPosition = Math.max(0, Math.min(newPosition, maxScroll));

      setCurrentScrollPosition(clampedPosition);

      // Professional friction value (like iOS: 0.98-0.99 for natural deceleration)
      scrollVelocity.current *= 0.98;

      // Gentle bounce back if over-scrolled (much softer than before)
      if (newPosition < 0 || newPosition > maxScroll) {
        scrollVelocity.current *= -0.1; // Much gentler bounce-back
      }
    }

    momentumAnimationRef.current = requestAnimationFrame(applyMomentum);
  }, [currentScrollPosition, setCurrentScrollPosition]);

  // Scroll by amount with professional momentum scaling
  const scrollBy = useCallback((amount: number, withMomentum: boolean = false) => {
    if (withMomentum) {
      // Increased velocity scaling for more responsive momentum (like native scrolling)
      scrollVelocity.current += amount * 0.25; // Increased from 0.1 to 0.25
      if (!isScrolling) {
        setIsScrolling(true);
        applyMomentum();
      }
    } else {
      // Direct smooth scroll with faster response
      smoothScrollTo(currentScrollPosition + amount, 120); // Reduced from 150ms to 120ms
    }
  }, [currentScrollPosition, smoothScrollTo, applyMomentum, isScrolling]);

  // Handle wheel events with professional responsiveness
  const handleWheel = useCallback((e: WheelEvent) => {
    e.preventDefault();

    const now = performance.now();
    const timeDelta = now - lastWheelTime.current;
    lastWheelTime.current = now;

    // Natural scroll amount based on wheel delta
    let scrollAmount = e.deltaY;

    // Adjust for different wheel types
    if (e.deltaMode === 1) { // Line mode
      scrollAmount *= 20;
    } else if (e.deltaMode === 2) { // Page mode
      scrollAmount *= 100;
    }

    // Professional momentum detection and scaling (like macOS/iOS)
    if (timeDelta < 50) {
      // Rapid scrolling - use momentum with higher responsiveness
      scrollBy(scrollAmount * 0.6, true); // Increased from 0.3 to 0.6
    } else {
      // Deliberate scrolling - direct scroll with better responsiveness
      scrollBy(scrollAmount * 0.8, false); // Increased from 0.5 to 0.8
    }
  }, [scrollBy]);

  // Handle touch events for mobile with natural momentum
  const [touchStart, setTouchStart] = useState<{ y: number; time: number } | null>(null);
  const [touchVelocity, setTouchVelocity] = useState<number>(0);

  const handleTouchStart = useCallback((e: TouchEvent) => {
    // Stop any ongoing momentum
    if (momentumAnimationRef.current) {
      cancelAnimationFrame(momentumAnimationRef.current);
    }
    scrollVelocity.current = 0;

    setTouchStart({
      y: e.touches[0].clientY,
      time: performance.now()
    });
  }, []);

  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (!touchStart) return;

    const touchCurrent = e.touches[0].clientY;
    const diff = touchStart.y - touchCurrent;
    const timeDiff = performance.now() - touchStart.time;

    if (Math.abs(diff) > 1) { // Even lower threshold for ultra-smooth touch response
      e.preventDefault();

      // Calculate velocity for momentum
      const velocity = diff / Math.max(timeDiff, 1);
      setTouchVelocity(velocity);

      // More responsive direct scroll during touch (like native mobile apps)
      scrollBy(diff * 1.0, false); // Increased from 0.8 to 1.0 for 1:1 touch response

      // Update touch start for next calculation
      setTouchStart({
        y: touchCurrent,
        time: performance.now()
      });
    }
  }, [touchStart, scrollBy]);

  const handleTouchEnd = useCallback(() => {
    if (touchStart && Math.abs(touchVelocity) > 0.05) { // Lower threshold for momentum
      // Professional momentum scaling (like iOS/Android native apps)
      scrollVelocity.current = touchVelocity * 30; // Increased from 20 to 30 for better momentum
      setIsScrolling(true);
      applyMomentum();
    }

    setTouchStart(null);
    setTouchVelocity(0);
  }, [touchStart, touchVelocity, applyMomentum]);

  // Update scroll state when collapsed state or position changes
  useEffect(() => {
    updateScrollState();
  }, [currentScrollPosition, isCollapsed, updateScrollState]);

  // Cleanup animations on unmount
  useEffect(() => {
    return () => {
      if (scrollAnimationRef.current) {
        cancelAnimationFrame(scrollAnimationRef.current);
      }
      if (momentumAnimationRef.current) {
        cancelAnimationFrame(momentumAnimationRef.current);
      }
    };
  }, []);

  return {
    scrollPosition: currentScrollPosition,
    expandedScrollPosition,
    collapsedScrollPosition,
    canScrollUp,
    canScrollDown,
    isScrolling,
    scrollContainerRef,
    contentRef,
    scrollBy,
    smoothScrollTo,
    handleWheel,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
    updateScrollState
  };
}

export default function AdminSidebar({
  isOpen,
  onClose,
  isCollapsed = false,
  onExpandAndNavigate
}: AdminSidebarProps) {
  const pathname = usePathname();

  // Initialize JavaScript-based scrolling with independent states
  const {
    scrollPosition,
    expandedScrollPosition,
    collapsedScrollPosition,
    canScrollUp,
    canScrollDown,
    isScrolling,
    scrollContainerRef,
    contentRef,
    scrollBy,
    smoothScrollTo,
    handleWheel,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
    updateScrollState
  } = useJavaScriptScrolling(isCollapsed);

  // Setup event listeners for custom scrolling
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    // Add wheel event listener
    container.addEventListener('wheel', handleWheel, { passive: false });

    // Add touch event listeners for mobile
    container.addEventListener('touchstart', handleTouchStart, { passive: false });
    container.addEventListener('touchmove', handleTouchMove, { passive: false });
    container.addEventListener('touchend', handleTouchEnd);

    // Update scroll state on mount and resize
    updateScrollState();
    window.addEventListener('resize', updateScrollState);

    return () => {
      container.removeEventListener('wheel', handleWheel);
      container.removeEventListener('touchstart', handleTouchStart);
      container.removeEventListener('touchmove', handleTouchMove);
      container.removeEventListener('touchend', handleTouchEnd);
      window.removeEventListener('resize', updateScrollState);
    };
  }, [handleWheel, handleTouchStart, handleTouchMove, handleTouchEnd, updateScrollState]);

  // Update scroll state when content changes
  useEffect(() => {
    const timer = setTimeout(updateScrollState, 100);
    return () => clearTimeout(timer);
  }, [isCollapsed, updateScrollState]);

  const isItemActive = (href: string) => {
    return pathname === href || (href === '/admin/dashboard' && pathname === '/admin');
  };

  const handleCollapsedCategoryClick = (category: NavigationCategory) => {
    if (onExpandAndNavigate && category.items.length > 0) {
      // Get the first item in the category
      const firstItem = category.items[0];
      // Expand sidebar and navigate to first item
      onExpandAndNavigate(firstItem.href, category.name);
    }
  };

  // Handle navigation link clicks - only close sidebar on mobile
  const handleNavClick = () => {
    // Only close sidebar on mobile (when screen is small)
    if (window.innerWidth < 1024) { // lg breakpoint
      onClose();
    }
  };

  // Scroll control functions with professional responsiveness
  const handleScrollUp = () => {
    scrollBy(-60, false); // Reduced from 80px to 60px for finer control
  };

  const handleScrollDown = () => {
    scrollBy(60, false); // Reduced from 80px to 60px for finer control
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen && window.innerWidth < 1024) return; // Only when sidebar is visible

      switch (e.key) {
        case 'ArrowUp':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            handleScrollUp();
          }
          break;
        case 'ArrowDown':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            handleScrollDown();
          }
          break;
        case 'Home':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            smoothScrollTo(0);
          }
          break;
        case 'End':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            const content = contentRef.current;
            const container = scrollContainerRef.current;
            if (content && container) {
              smoothScrollTo(content.scrollHeight - container.clientHeight);
            }
          }
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, scrollBy, smoothScrollTo, contentRef, scrollContainerRef]);

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div className={`fixed bottom-0 left-0 z-50 bg-gray-50 shadow-lg transform transition-all duration-300 ease-in-out lg:translate-x-0 ${
        isOpen ? 'translate-x-0' : '-translate-x-full'
      } ${isCollapsed ? 'w-16' : 'w-64'}`} style={{ top: '67px' }}>
        {/* Mobile close button */}
        <div className="lg:hidden flex justify-end p-2 border-b border-gray-200 bg-gray-50">
          <button
            onClick={onClose}
            className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Custom JavaScript Scrolling Container */}
        <div
          ref={scrollContainerRef}
          className="relative h-full overflow-hidden"
          style={{
            cursor: isScrolling ? 'grabbing' : 'default',
            userSelect: 'none'
          }}
        >
          {/* Scroll Up Button */}
          {canScrollUp && !isCollapsed && (
            <div className="absolute top-0 left-0 right-0 z-10 bg-gradient-to-b from-gray-50 to-transparent h-8 flex items-start justify-center pt-1">
              <button
                onClick={handleScrollUp}
                className="p-1 rounded-full bg-white shadow-sm border border-gray-200 text-gray-400 hover:text-gray-600 hover:bg-gray-50 transition-colors"
                title="Scroll up (Ctrl+↑)"
              >
                <ChevronUpIcon className="h-4 w-4" />
              </button>
            </div>
          )}

          {/* Scroll Down Button */}
          {canScrollDown && !isCollapsed && (
            <div className="absolute bottom-0 left-0 right-0 z-10 bg-gradient-to-t from-gray-50 to-transparent h-8 flex items-end justify-center pb-1">
              <button
                onClick={handleScrollDown}
                className="p-1 rounded-full bg-white shadow-sm border border-gray-200 text-gray-400 hover:text-gray-600 hover:bg-gray-50 transition-colors"
                title="Scroll down (Ctrl+↓)"
              >
                <ChevronDownIcon className="h-4 w-4" />
              </button>
            </div>
          )}

          {/* Scrollable Content */}
          <div
            ref={contentRef}
            className="h-full"
            style={{
              transform: `translateY(-${scrollPosition}px)`,
              transition: isScrolling ? 'none' : 'transform 0.03s ease-out', // Faster transition for more responsive feel
              willChange: 'transform'
            }}
          >
            {/* Navigation */}
            <nav className={`py-3 ${isCollapsed ? 'px-2' : 'px-4'}`}>
            <div className="space-y-1">
              {navigationCategories.map((category) => {

                if (isCollapsed) {
                  // Collapsed view - show only category icons (interactive)
                  return (
                    <div key={category.name} className="relative group">
                      <button
                        onClick={() => handleCollapsedCategoryClick(category)}
                        className="w-full flex items-center justify-center p-3 rounded-md transition-all duration-200 hover:scale-105 text-gray-600 hover:bg-orange-50 hover:text-orange-600 hover:shadow-sm"
                        title={`${category.name} - Click to expand and view ${category.items[0]?.name || 'items'}`}
                      >
                        <category.icon className="h-6 w-6" />
                      </button>

                      {/* Enhanced Tooltip with interaction hint */}
                      <div className="absolute left-full ml-3 top-0 bg-gray-900 text-white text-sm px-3 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50 shadow-lg">
                        <div className="font-medium">{category.name}</div>
                        <div className="text-xs text-gray-300 mt-1">
                          Click to expand & go to {category.items[0]?.name || 'first item'}
                        </div>
                        <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-gray-900 rotate-45"></div>
                      </div>
                    </div>
                  );
                }

                // Expanded view - show full categories and items (always visible)
                return (
                  <div key={category.name}>
                    {/* Category Header - Static Label */}
                    <div className="category-header px-3 py-2 text-gray-700">
                      <span className="category-text uppercase tracking-wide text-sm font-bold leading-tight">
                        {category.name}
                      </span>
                    </div>

                    {/* Category Items - Always Visible */}
                    <div className="ml-3 space-y-1 mt-1 mb-4">
                      {category.items.map((item) => {
                        const isActive = isItemActive(item.href);
                        return (
                          <Link
                            key={item.name}
                            href={item.href}
                            onClick={handleNavClick}
                            className={`group flex items-center px-3 py-2.5 text-sm font-medium rounded-md transition-colors ${
                              isActive
                                ? 'bg-orange-100 text-orange-700 border-r-2 border-orange-500'
                                : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                            }`}
                          >
                            <item.icon
                              className={`mr-3 h-4 w-4 ${
                                isActive ? 'text-orange-500' : 'text-gray-400 group-hover:text-gray-500'
                              }`}
                            />
                            <span className="text-sm font-normal">{item.name}</span>
                          </Link>
                        );
                      })}
                    </div>
                  </div>
                );
              })}
            </div>
          </nav>

            {/* Bottom Spacer for better scrolling experience */}
            <div className="h-6"></div>
          </div>

          {/* Custom Scroll Indicator (for collapsed mode) */}
          {isCollapsed && (canScrollUp || canScrollDown) && (
            <div className="absolute right-1 top-1/2 transform -translate-y-1/2 flex flex-col space-y-1">
              {canScrollUp && (
                <button
                  onClick={handleScrollUp}
                  className="w-2 h-2 rounded-full bg-gray-300 hover:bg-gray-400 transition-colors"
                  title="Scroll up"
                />
              )}
              {canScrollDown && (
                <button
                  onClick={handleScrollDown}
                  className="w-2 h-2 rounded-full bg-gray-300 hover:bg-gray-400 transition-colors"
                  title="Scroll down"
                />
              )}
            </div>
          )}
        </div>
      </div>
    </>
  );
}
